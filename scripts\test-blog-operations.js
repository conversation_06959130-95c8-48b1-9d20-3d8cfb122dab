/**
 * Test Blog Operations
 * Demonstrates blog CRUD operations with the clean Neon setup
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { neon } = require('@neondatabase/serverless');

async function testBlogOperations() {
  try {
    console.log('📝 Testing Blog Operations\n');
    
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL not found');
      process.exit(1);
    }
    
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔌 Testing connection...');
    await sql`SELECT NOW() as current_time`;
    console.log('✅ Connection successful!');
    
    // Create a sample blog post
    console.log('\n📝 Creating sample blog post...');
    const newPost = await sql`
      INSERT INTO blog_posts (
        title, slug, content, excerpt, author_name,
        categories, tags, is_published, is_featured
      ) VALUES (
        'Welcome to Tap2Go Blog',
        'welcome-to-tap2go-blog',
        'This is our first blog post! We are excited to share food stories, restaurant features, and culinary adventures with you.',
        'Welcome to the official Tap2Go blog where we share amazing food stories.',
        'Tap2Go Team',
        ${JSON.stringify(['announcements', 'company'])},
        ${JSON.stringify(['welcome', 'food', 'delivery'])},
        true,
        true
      )
      RETURNING *
    `;
    
    console.log('✅ Blog post created:', newPost[0].title);
    
    // Create another blog post
    console.log('\n📝 Creating another blog post...');
    const secondPost = await sql`
      INSERT INTO blog_posts (
        title, slug, content, excerpt, author_name,
        categories, tags, is_published, is_featured
      ) VALUES (
        'Top 10 Filipino Dishes You Must Try',
        'top-10-filipino-dishes',
        'Discover the rich flavors of Filipino cuisine with our curated list of must-try dishes available on Tap2Go.',
        'Explore the best Filipino dishes available for delivery.',
        'Chef Maria Santos',
        ${JSON.stringify(['food-guide', 'filipino-cuisine'])},
        ${JSON.stringify(['filipino', 'cuisine', 'food-guide', 'adobo', 'lechon'])},
        true,
        false
      )
      RETURNING *
    `;
    
    console.log('✅ Second blog post created:', secondPost[0].title);
    
    // Test reading blog posts
    console.log('\n📖 Reading all published blog posts...');
    const allPosts = await sql`
      SELECT id, title, slug, author_name, is_featured, created_at
      FROM blog_posts 
      WHERE is_published = true
      ORDER BY created_at DESC
    `;
    
    console.log(`✅ Found ${allPosts.length} published posts:`);
    allPosts.forEach(post => {
      console.log(`   ${post.is_featured ? '⭐' : '📄'} ${post.title} by ${post.author_name}`);
    });
    
    // Test search functionality
    console.log('\n🔍 Testing search functionality...');
    const searchResults = await sql`
      SELECT title, excerpt
      FROM blog_posts
      WHERE (title ILIKE '%filipino%' OR content ILIKE '%filipino%')
      AND is_published = true
    `;
    
    console.log(`✅ Search for "filipino" found ${searchResults.length} results:`);
    searchResults.forEach(post => {
      console.log(`   📄 ${post.title}`);
    });
    
    // Test category filtering
    console.log('\n🏷️  Testing category filtering...');
    const categoryPosts = await sql`
      SELECT title, categories
      FROM blog_posts
      WHERE categories @> ${JSON.stringify(['food-guide'])}
      AND is_published = true
    `;
    
    console.log(`✅ Posts in "food-guide" category: ${categoryPosts.length}`);
    categoryPosts.forEach(post => {
      console.log(`   📄 ${post.title}`);
    });
    
    // Test featured posts
    console.log('\n⭐ Testing featured posts...');
    const featuredPosts = await sql`
      SELECT title, is_featured
      FROM blog_posts
      WHERE is_featured = true AND is_published = true
      ORDER BY created_at DESC
    `;
    
    console.log(`✅ Featured posts: ${featuredPosts.length}`);
    featuredPosts.forEach(post => {
      console.log(`   ⭐ ${post.title}`);
    });
    
    // Test blog statistics
    console.log('\n📊 Getting blog statistics...');
    const stats = await sql`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_published = true) as published,
        COUNT(*) FILTER (WHERE is_featured = true) as featured,
        COUNT(*) FILTER (WHERE is_published = false) as drafts
      FROM blog_posts
    `;
    
    const blogStats = stats[0];
    console.log('✅ Blog Statistics:');
    console.log(`   📄 Total posts: ${blogStats.total}`);
    console.log(`   ✅ Published: ${blogStats.published}`);
    console.log(`   ⭐ Featured: ${blogStats.featured}`);
    console.log(`   📝 Drafts: ${blogStats.drafts}`);
    
    // Test updating a post
    console.log('\n✏️  Testing post update...');
    const updatedPost = await sql`
      UPDATE blog_posts 
      SET 
        excerpt = 'Updated: Welcome to the official Tap2Go blog - your source for food stories!',
        updated_at = NOW()
      WHERE slug = 'welcome-to-tap2go-blog'
      RETURNING title, excerpt
    `;
    
    console.log('✅ Post updated:', updatedPost[0].title);
    
    console.log('\n🎉 All blog operations tested successfully!');
    console.log('\n📋 Blog Features Verified:');
    console.log('  ✅ Create blog posts');
    console.log('  ✅ Read published posts');
    console.log('  ✅ Search functionality');
    console.log('  ✅ Category filtering');
    console.log('  ✅ Featured posts');
    console.log('  ✅ Blog statistics');
    console.log('  ✅ Update posts');
    
    console.log('\n🎯 Architecture Status:');
    console.log('  🔥 Firestore: Handles all business logic');
    console.log('  🗄️  Neon PostgreSQL: Blog posts only');
    console.log('  ✅ Clean separation achieved!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testBlogOperations();
