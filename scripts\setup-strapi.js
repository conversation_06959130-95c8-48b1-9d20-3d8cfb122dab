#!/usr/bin/env node

/**
 * Strapi Setup Script for Tap2Go CMS Integration
 * Creates and configures Strapi project with Neon PostgreSQL
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt user for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Function to run shell commands
function runCommand(command, description, cwd = process.cwd()) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    throw error;
  }
}

// Function to create Strapi project
async function createStrapiProject() {
  const projectName = 'tap2go-cms';
  const projectPath = path.join(process.cwd(), projectName);
  
  // Check if project already exists
  if (fs.existsSync(projectPath)) {
    console.log(`📁 Strapi project '${projectName}' already exists.`);
    const overwrite = await prompt('Do you want to overwrite it? (y/N): ');
    
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Setup cancelled.');
      return projectPath;
    }
    
    // Remove existing project
    fs.rmSync(projectPath, { recursive: true, force: true });
  }
  
  // Create new Strapi project
  const createCommand = `npx create-strapi-app@latest ${projectName} --quickstart --no-run`;
  runCommand(createCommand, 'Creating Strapi project');
  
  return projectPath;
}

// Function to install additional Strapi dependencies
function installStrapiDependencies(projectPath) {
  const dependencies = [
    'pg',                                    // PostgreSQL client
    '@strapi/provider-upload-cloudinary',   // Cloudinary upload provider
    'strapi-plugin-seo',                    // SEO plugin
    'strapi-plugin-slugify',                // Slug generation plugin
  ];
  
  const installCommand = `npm install ${dependencies.join(' ')}`;
  runCommand(installCommand, 'Installing Strapi dependencies', projectPath);
}

// Function to create Strapi configuration files
function createStrapiConfig(projectPath, databaseUrl) {
  // Database configuration
  const databaseConfig = `module.exports = ({ env }) => ({
  connection: {
    client: 'postgres',
    connection: {
      connectionString: env('DATABASE_URL'),
      ssl: env.bool('DATABASE_SSL', true) && {
        rejectUnauthorized: false,
      },
    },
    pool: {
      min: env.int('DATABASE_POOL_MIN', 2),
      max: env.int('DATABASE_POOL_MAX', 10),
    },
    acquireConnectionTimeout: env.int('DATABASE_CONNECTION_TIMEOUT', 60000),
  },
});`;

  // Server configuration
  const serverConfig = `module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
  webhooks: {
    populateRelations: env.bool('WEBHOOKS_POPULATE_RELATIONS', false),
  },
});`;

  // Cloudinary plugin configuration
  const pluginsConfig = `module.exports = ({ env }) => ({
  upload: {
    config: {
      provider: 'cloudinary',
      providerOptions: {
        cloud_name: env('CLOUDINARY_NAME'),
        api_key: env('CLOUDINARY_KEY'),
        api_secret: env('CLOUDINARY_SECRET'),
      },
      actionOptions: {
        upload: {},
        uploadStream: {},
        delete: {},
      },
    },
  },
  seo: {
    enabled: true,
  },
  slugify: {
    enabled: true,
    config: {
      contentTypes: {
        'api::restaurant-content.restaurant-content': {
          field: 'slug',
          references: 'name',
        },
        'api::blog-post.blog-post': {
          field: 'slug',
          references: 'title',
        },
        'api::static-page.static-page': {
          field: 'slug',
          references: 'title',
        },
      },
    },
  },
});`;

  // Middleware configuration
  const middlewareConfig = `module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': [
            "'self'",
            'data:',
            'blob:',
            'res.cloudinary.com',
          ],
          'media-src': [
            "'self'",
            'data:',
            'blob:',
            'res.cloudinary.com',
          ],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];`;

  // Create config directories
  const configPath = path.join(projectPath, 'config');
  fs.mkdirSync(configPath, { recursive: true });

  // Write configuration files
  fs.writeFileSync(path.join(configPath, 'database.js'), databaseConfig);
  fs.writeFileSync(path.join(configPath, 'server.js'), serverConfig);
  fs.writeFileSync(path.join(configPath, 'plugins.js'), pluginsConfig);
  fs.writeFileSync(path.join(configPath, 'middlewares.js'), middlewareConfig);

  console.log('✅ Strapi configuration files created\n');
}

// Function to create Strapi environment file
function createStrapiEnv(projectPath, databaseUrl, cloudinaryConfig) {
  const envContent = `# Strapi Environment Configuration
HOST=0.0.0.0
PORT=1337

# Database
DATABASE_URL=${databaseUrl}
DATABASE_SSL=true
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_CONNECTION_TIMEOUT=60000

# Cloudinary
CLOUDINARY_NAME=${cloudinaryConfig.cloudName}
CLOUDINARY_KEY=${cloudinaryConfig.apiKey}
CLOUDINARY_SECRET=${cloudinaryConfig.apiSecret}

# Strapi Security (Generate these in production)
APP_KEYS=toBeModified1,toBeModified2
API_TOKEN_SALT=toBeModified
ADMIN_JWT_SECRET=toBeModified
TRANSFER_TOKEN_SALT=toBeModified

# Webhooks
WEBHOOKS_POPULATE_RELATIONS=false
`;

  fs.writeFileSync(path.join(projectPath, '.env'), envContent);
  console.log('✅ Strapi .env file created\n');
}

// Function to create initial content types schema
function createContentTypesSchema(projectPath) {
  const schemaPath = path.join(projectPath, 'src', 'api');
  
  // This would create the initial schema files
  // For now, we'll just create the directory structure
  fs.mkdirSync(schemaPath, { recursive: true });
  
  console.log('✅ Content types directory structure created\n');
  console.log('📝 Note: Content types will be created through Strapi admin panel');
}

// Main setup function
async function setupStrapi() {
  try {
    console.log('🚀 Setting up Strapi CMS for Tap2Go...\n');
    
    // Get database URL from user
    console.log('📋 Please provide your Neon database configuration:');
    const databaseUrl = await prompt('Neon Database URL: ');
    
    if (!databaseUrl) {
      console.error('❌ Database URL is required');
      process.exit(1);
    }
    
    // Get Cloudinary configuration
    console.log('\n📋 Cloudinary configuration (from your existing .env.local):');
    const cloudinaryConfig = {
      cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'dpekh75yi',
      apiKey: process.env.CLOUDINARY_API_KEY || '191284661715922',
      apiSecret: process.env.CLOUDINARY_API_SECRET || 'G-_izp68I2eJuZOCvAKOmPkTXdI'
    };
    
    console.log(`Cloud Name: ${cloudinaryConfig.cloudName}`);
    console.log(`API Key: ${cloudinaryConfig.apiKey}`);
    console.log('API Secret: [HIDDEN]');
    
    const useExisting = await prompt('\nUse existing Cloudinary config? (Y/n): ');
    
    if (useExisting.toLowerCase() === 'n') {
      cloudinaryConfig.cloudName = await prompt('Cloudinary Cloud Name: ');
      cloudinaryConfig.apiKey = await prompt('Cloudinary API Key: ');
      cloudinaryConfig.apiSecret = await prompt('Cloudinary API Secret: ');
    }
    
    // Create Strapi project
    const projectPath = await createStrapiProject();
    
    // Install dependencies
    installStrapiDependencies(projectPath);
    
    // Create configuration files
    createStrapiConfig(projectPath, databaseUrl);
    createStrapiEnv(projectPath, databaseUrl, cloudinaryConfig);
    createContentTypesSchema(projectPath);
    
    console.log('🎉 Strapi setup completed successfully!\n');
    console.log('📋 Next Steps:');
    console.log('1. Update your main .env.local file with Strapi configuration');
    console.log('2. Start Strapi: npm run strapi:dev');
    console.log('3. Access Strapi admin: http://localhost:1337/admin');
    console.log('4. Create your first admin user');
    console.log('5. Set up content types as described in the integration plan');
    console.log('\n📖 See docs/ENHANCED_STRAPI_INTEGRATION_PLAN.md for detailed instructions.');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the setup
setupStrapi();
