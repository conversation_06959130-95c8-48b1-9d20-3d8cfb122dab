# 🚀 Tap2Go Strapi + Neon Integration Plan - Part 2
## Implementation Phases 5-8

---

## 🎨 Phase 5: Frontend Integration (Week 5-6)

### 5.1 Component Updates

**Enhanced Restaurant Card with CMS Content:**
```typescript
// src/components/RestaurantCard.tsx (Enhanced)
import { useGetRestaurantContentQuery } from '../store/api/strapiApi';
import { useGetHybridRestaurantQuery } from '../store/api/hybridApi';

interface RestaurantCardProps {
  restaurant: Restaurant;
  showContent?: boolean;
}

export function RestaurantCard({ restaurant, showContent = false }: RestaurantCardProps) {
  // Get operational data (existing)
  const operationalData = restaurant;
  
  // Get content data from Strapi
  const { data: contentData, isLoading: contentLoading } = useGetRestaurantContentQuery(
    restaurant.id,
    { skip: !showContent }
  );
  
  // Or get hybrid data
  const { data: hybridData, isLoading: hybridLoading } = useGetHybridRestaurantQuery(
    restaurant.id,
    { skip: !showContent }
  );

  return (
    <div className="restaurant-card">
      {/* Operational Data (Firebase) */}
      <div className="operational-info">
        <h3>{operationalData.name}</h3>
        <p>Rating: {operationalData.rating}</p>
        <p>Delivery: {operationalData.deliveryTime}</p>
        <p>Status: {operationalData.isOpen ? 'Open' : 'Closed'}</p>
      </div>
      
      {/* Content Data (Strapi) */}
      {showContent && contentData && (
        <div className="content-info">
          <div className="gallery">
            {contentData.data.attributes.gallery?.data?.map((image) => (
              <img key={image.id} src={image.attributes.url} alt="" />
            ))}
          </div>
          <p className="story">{contentData.data.attributes.story}</p>
          <div className="features">
            {contentData.data.attributes.features?.data?.map((feature) => (
              <span key={feature.id} className="feature-tag">
                {feature.attributes.name}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

### 5.2 Menu Management Integration

**Enhanced Menu Components:**
```typescript
// src/components/MenuSection.tsx
import { useGetMenuCategoriesQuery, useGetMenuItemsQuery } from '../store/api/strapiApi';

export function MenuSection({ restaurantId }: { restaurantId: string }) {
  const { data: categories, isLoading } = useGetMenuCategoriesQuery(restaurantId);
  
  if (isLoading) return <div>Loading menu...</div>;
  
  return (
    <div className="menu-section">
      {categories?.data?.map((category) => (
        <MenuCategory key={category.id} category={category} />
      ))}
    </div>
  );
}

function MenuCategory({ category }) {
  const { data: menuItems } = useGetMenuItemsQuery(category.id);
  
  return (
    <div className="menu-category">
      <h3>{category.attributes.name}</h3>
      <p>{category.attributes.description}</p>
      
      <div className="menu-items">
        {menuItems?.data?.map((item) => (
          <MenuItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
}
```

### 5.3 Promotional Content Integration

**Promotions Component:**
```typescript
// src/components/PromotionsSection.tsx
import { useGetActivePromotionsQuery } from '../store/api/strapiApi';

export function PromotionsSection() {
  const { data: promotions, isLoading } = useGetActivePromotionsQuery();
  
  if (isLoading) return <div>Loading promotions...</div>;
  
  return (
    <div className="promotions-section">
      <h2>Current Promotions</h2>
      <div className="promotions-grid">
        {promotions?.data?.map((promotion) => (
          <PromotionCard key={promotion.id} promotion={promotion} />
        ))}
      </div>
    </div>
  );
}

function PromotionCard({ promotion }) {
  const { attributes } = promotion;
  
  return (
    <div className="promotion-card">
      <img src={attributes.image?.data?.attributes?.url} alt={attributes.title} />
      <div className="promotion-content">
        <h3>{attributes.title}</h3>
        <p>{attributes.description}</p>
        <div className="promotion-details">
          <span className="discount">{attributes.discountValue}% OFF</span>
          <span className="validity">Valid until: {attributes.validUntil}</span>
        </div>
        {attributes.promoCode && (
          <div className="promo-code">
            Code: <strong>{attributes.promoCode}</strong>
          </div>
        )}
      </div>
    </div>
  );
}
```

---

## 🔐 Phase 6: Security & Performance (Week 6-7)

### 6.1 API Security

**Strapi Security Configuration:**
```javascript
// tap2go-cms/config/middlewares.js
module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'res.cloudinary.com'],
          'media-src': ["'self'", 'data:', 'blob:', 'res.cloudinary.com'],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

**API Rate Limiting:**
```typescript
// src/lib/strapi/client.ts (Enhanced)
import rateLimit from 'axios-rate-limit';

export class StrapiClient {
  private client;
  
  constructor() {
    this.client = rateLimit(axios.create({
      baseURL: process.env.STRAPI_URL,
      headers: {
        'Authorization': `Bearer ${process.env.STRAPI_API_TOKEN}`,
      },
    }), { 
      maxRequests: 100, 
      perMilliseconds: 60000 // 100 requests per minute
    });
  }
}
```

### 6.2 Caching Strategy

**Redis Integration for Strapi:**
```typescript
// src/lib/strapi/cache.ts
import Redis from 'ioredis';

class StrapiCache {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }
  
  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }
  
  async set(key: string, data: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }
  
  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
  
  // Cache restaurant content for 1 hour
  async cacheRestaurantContent(restaurantId: string, data: any): Promise<void> {
    await this.set(`restaurant:content:${restaurantId}`, data, 3600);
  }
  
  // Cache menu data for 30 minutes
  async cacheMenuData(restaurantId: string, data: any): Promise<void> {
    await this.set(`menu:${restaurantId}`, data, 1800);
  }
}

export const strapiCache = new StrapiCache();
```

### 6.3 Performance Optimization

**Image Optimization with Strapi + Cloudinary:**
```typescript
// src/lib/strapi/media.ts
export class StrapiMediaOptimizer {
  static optimizeImage(imageUrl: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  } = {}) {
    const { width = 800, height, quality = 80, format = 'webp' } = options;
    
    // If it's a Cloudinary URL, apply transformations
    if (imageUrl.includes('res.cloudinary.com')) {
      const transformations = [
        `w_${width}`,
        height ? `h_${height}` : '',
        `q_${quality}`,
        `f_${format}`,
        'c_fill'
      ].filter(Boolean).join(',');
      
      return imageUrl.replace('/upload/', `/upload/${transformations}/`);
    }
    
    return imageUrl;
  }
  
  static getResponsiveImageSet(imageUrl: string) {
    return {
      mobile: this.optimizeImage(imageUrl, { width: 400 }),
      tablet: this.optimizeImage(imageUrl, { width: 768 }),
      desktop: this.optimizeImage(imageUrl, { width: 1200 }),
    };
  }
}
```

---

## 📊 Phase 7: Analytics & Monitoring (Week 7-8)

### 7.1 Performance Monitoring

**Strapi Performance Tracking:**
```typescript
// src/lib/strapi/monitoring.ts
import { performance } from 'perf_hooks';

export class StrapiMonitoring {
  static async trackQuery(queryName: string, queryFn: () => Promise<any>) {
    const startTime = performance.now();
    
    try {
      const result = await queryFn();
      const endTime = performance.now();
      
      // Log performance metrics
      console.log(`Strapi Query: ${queryName} - ${endTime - startTime}ms`);
      
      // Send to analytics (Firebase Analytics)
      if (typeof window !== 'undefined') {
        // Track in browser
        gtag('event', 'strapi_query', {
          query_name: queryName,
          duration: endTime - startTime,
          status: 'success'
        });
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      
      console.error(`Strapi Query Error: ${queryName} - ${endTime - startTime}ms`, error);
      
      // Track errors
      if (typeof window !== 'undefined') {
        gtag('event', 'strapi_query_error', {
          query_name: queryName,
          duration: endTime - startTime,
          error_message: error.message
        });
      }
      
      throw error;
    }
  }
}
```

### 7.2 Content Analytics

**Track Content Performance:**
```typescript
// src/lib/analytics/content.ts
export class ContentAnalytics {
  static trackContentView(contentType: string, contentId: string) {
    // Track content views in Firebase Analytics
    gtag('event', 'content_view', {
      content_type: contentType,
      content_id: contentId,
      source: 'strapi'
    });
  }
  
  static trackMenuItemView(restaurantId: string, itemId: string) {
    gtag('event', 'menu_item_view', {
      restaurant_id: restaurantId,
      item_id: itemId,
      source: 'strapi'
    });
  }
  
  static trackPromotionView(promotionId: string) {
    gtag('event', 'promotion_view', {
      promotion_id: promotionId,
      source: 'strapi'
    });
  }
}
```
