# 🚀 **Enhanced Tap2Go Strapi Integration - Part 2**
## **Advanced Implementation & Custom CMS Interface**

---

## 🎨 **Phase 4: Custom CMS Menu Interface for Admin Panel (Week 4-5)**

### **4.1 Seamless Menu Management Integration**
Building on your existing vendor menu management (`src/app/vendor/menu/page.tsx`):

```typescript
// src/app/admin/cms/menus/[restaurantId]/page.tsx
// Enhanced menu management with CMS content integration
'use client';

import { useGetMenuItemsQuery } from '@/store/api/apiSlice';
import { useGetMenuContentQuery, useUpdateMenuContentMutation } from '@/store/api/cmsApi';
import { useCloudinaryUpload } from '@/hooks/useCloudinaryUpload';

export default function AdminMenuContentManagement({ params }: { params: { restaurantId: string } }) {
  // Get operational menu data (Firebase)
  const { data: operationalMenu, isLoading: menuLoading } = useGetMenuItemsQuery(params.restaurantId);
  
  // Get content data (Strapi)
  const { data: menuContent, isLoading: contentLoading } = useGetMenuContentQuery(params.restaurantId);
  
  // Merge operational and content data
  const hybridMenuItems = useMemo(() => {
    return operationalMenu?.map(item => ({
      ...item,
      content: menuContent?.find(content => content.firebaseId === item.id),
      hasRichContent: !!menuContent?.find(content => content.firebaseId === item.id)
    }));
  }, [operationalMenu, menuContent]);

  return (
    <div className="admin-menu-cms">
      <div className="header">
        <h1>Menu Content Management</h1>
        <p>Enhance menu items with rich content, detailed descriptions, and professional photography</p>
      </div>

      {/* Menu Categories */}
      <div className="menu-categories">
        {groupBy(hybridMenuItems, 'category').map(([category, items]) => (
          <MenuCategorySection 
            key={category}
            category={category}
            items={items}
            onUpdateContent={handleUpdateContent}
          />
        ))}
      </div>

      {/* Bulk Actions */}
      <div className="bulk-actions">
        <button onClick={handleBulkImageUpload} className="btn-secondary">
          Bulk Upload Images
        </button>
        <button onClick={handleBulkContentGeneration} className="btn-primary">
          Generate AI Descriptions
        </button>
      </div>
    </div>
  );
}

// Enhanced Menu Item Card with CMS Integration
function MenuItemCMSCard({ item, onUpdateContent }) {
  const [isEditing, setIsEditing] = useState(false);
  const [updateContent] = useUpdateMenuContentMutation();
  
  return (
    <div className={`menu-item-cms-card ${item.hasRichContent ? 'has-content' : 'needs-content'}`}>
      {/* Operational Data (Firebase) - Read Only */}
      <div className="operational-section">
        <div className="item-header">
          <h3>{item.name}</h3>
          <div className="status-badges">
            <span className={`badge ${item.available ? 'available' : 'unavailable'}`}>
              {item.available ? 'Available' : 'Unavailable'}
            </span>
            <span className="price">${item.price}</span>
          </div>
        </div>
        <p className="basic-description">{item.description}</p>
      </div>

      {/* Content Section (Strapi) - Editable */}
      <div className="content-section">
        {item.hasRichContent ? (
          <RichContentDisplay content={item.content} onEdit={() => setIsEditing(true)} />
        ) : (
          <EmptyContentState onCreateContent={() => setIsEditing(true)} />
        )}
      </div>

      {/* Content Editor Modal */}
      {isEditing && (
        <MenuItemContentEditor
          item={item}
          onSave={handleSaveContent}
          onCancel={() => setIsEditing(false)}
        />
      )}
    </div>
  );
}
```

### **4.2 Real-time Synchronization System**
```typescript
// src/lib/cms/sync.ts
// Real-time sync between Firebase operational data and Strapi content
export class MenuSyncManager {
  private firebaseListeners: Map<string, () => void> = new Map();
  
  // Listen to Firebase menu changes and sync to Strapi
  setupMenuSync(restaurantId: string) {
    const menuRef = collection(db, `restaurants/${restaurantId}/menuItems`);
    
    const unsubscribe = onSnapshot(menuRef, async (snapshot) => {
      const changes = snapshot.docChanges();
      
      for (const change of changes) {
        const menuItem = { id: change.doc.id, ...change.doc.data() };
        
        switch (change.type) {
          case 'added':
            await this.createMenuItemContentPlaceholder(menuItem);
            break;
          case 'modified':
            await this.syncMenuItemChanges(menuItem);
            break;
          case 'removed':
            await this.archiveMenuItemContent(menuItem.id);
            break;
        }
      }
    });
    
    this.firebaseListeners.set(restaurantId, unsubscribe);
  }
  
  private async createMenuItemContentPlaceholder(menuItem: any) {
    // Create placeholder content in Strapi when new menu item is added in Firebase
    await strapiClient.post('/menu-item-contents', {
      data: {
        firebaseId: menuItem.id,
        name: menuItem.name,
        basicDescription: menuItem.description,
        status: 'draft',
        needsContent: true,
        createdFromFirebase: true
      }
    });
  }
  
  private async syncMenuItemChanges(menuItem: any) {
    // Update basic info in Strapi when Firebase data changes
    const strapiContent = await strapiClient.get('/menu-item-contents', {
      filters: { firebaseId: { $eq: menuItem.id } }
    });
    
    if (strapiContent.data.length > 0) {
      const contentId = strapiContent.data[0].id;
      await strapiClient.put(`/menu-item-contents/${contentId}`, {
        data: {
          name: menuItem.name,
          basicDescription: menuItem.description,
          price: menuItem.price,
          available: menuItem.available,
          lastSyncedAt: new Date().toISOString()
        }
      });
    }
  }
}
```

---

## 🔄 **Phase 5: Unified API Layer (Week 5-6)**

### **5.1 Hybrid Data Resolver**
```typescript
// src/lib/cms/hybrid.ts
// Unified data resolver that combines Firebase + Strapi + Cloudinary
export class TapGoHybridResolver {
  async getRestaurantComplete(restaurantId: string): Promise<CompleteRestaurant> {
    // Get operational data from Firebase
    const [operationalData, menuItems, reviews] = await Promise.all([
      this.getFirebaseRestaurant(restaurantId),
      this.getFirebaseMenuItems(restaurantId),
      this.getFirebaseReviews(restaurantId)
    ]);
    
    // Get content data from Strapi
    const [contentData, menuContent, promotions] = await Promise.all([
      this.getStrapiRestaurantContent(restaurantId),
      this.getStrapiMenuContent(restaurantId),
      this.getStrapiPromotions(restaurantId)
    ]);
    
    // Merge and optimize
    return {
      // Operational data (real-time)
      id: operationalData.id,
      isOpen: operationalData.isOpen,
      rating: operationalData.rating,
      deliveryTime: operationalData.deliveryTime,
      totalOrders: operationalData.totalOrders,
      
      // Content data (CMS)
      story: contentData?.story,
      gallery: contentData?.gallery?.map(img => 
        getRestaurantGalleryImage(img.publicId, 'medium')
      ),
      awards: contentData?.awards,
      features: contentData?.features,
      
      // Enhanced menu with content
      menu: this.mergeMenuWithContent(menuItems, menuContent),
      
      // Marketing content
      activePromotions: promotions?.filter(p => p.isActive),
      
      // Reviews (real-time)
      recentReviews: reviews?.slice(0, 5),
      
      // Metadata
      source: 'hybrid',
      lastUpdated: new Date().toISOString()
    };
  }
  
  private mergeMenuWithContent(menuItems: any[], menuContent: any[]): EnhancedMenuItem[] {
    return menuItems.map(item => {
      const content = menuContent.find(c => c.firebaseId === item.id);
      
      return {
        // Operational data
        id: item.id,
        name: item.name,
        price: item.price,
        available: item.available,
        category: item.category,
        
        // Enhanced content
        detailedDescription: content?.detailedDescription || item.description,
        ingredients: content?.ingredients || [],
        allergens: content?.allergens || [],
        nutritionalInfo: content?.nutritionalInfo,
        images: content?.images?.map(img => 
          getMenuItemImage(img.publicId)
        ) || [item.image],
        tags: content?.tags || [],
        
        // Metadata
        hasRichContent: !!content,
        lastContentUpdate: content?.updatedAt
      };
    });
  }
}
```

### **5.2 Enhanced API Routes**
```typescript
// src/app/api/hybrid/restaurant/[id]/route.ts
// Unified API endpoint that serves complete restaurant data
import { TapGoHybridResolver } from '@/lib/cms/hybrid';
import { NextRequest, NextResponse } from 'next/server';

const hybridResolver = new TapGoHybridResolver();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const includeMenu = searchParams.get('includeMenu') === 'true';
    const includeReviews = searchParams.get('includeReviews') === 'true';
    
    // Get complete restaurant data
    const restaurant = await hybridResolver.getRestaurantComplete(params.id);
    
    // Apply filters based on query params
    const response = {
      ...restaurant,
      menu: includeMenu ? restaurant.menu : undefined,
      recentReviews: includeReviews ? restaurant.recentReviews : undefined
    };
    
    // Cache response
    const cacheKey = `restaurant:${params.id}:${includeMenu}:${includeReviews}`;
    await cacheManager.set(cacheKey, response, 300); // 5 minutes
    
    return NextResponse.json({
      success: true,
      data: response,
      cached: false,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Hybrid restaurant API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch restaurant data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
```

---

## 📊 **Phase 6: Performance Optimization & Caching (Week 6-7)**

### **6.1 Multi-Layer Caching Strategy**
```typescript
// src/lib/cms/cache.ts
// Enterprise-grade caching for hybrid data
export class TapGoCacheManager {
  private redis: Redis;
  private memoryCache: Map<string, { data: any; expires: number }> = new Map();
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }
  
  // Cache restaurant content with different TTLs
  async cacheRestaurantData(restaurantId: string, data: any) {
    const cacheKeys = {
      operational: `restaurant:operational:${restaurantId}`,    // 5 minutes
      content: `restaurant:content:${restaurantId}`,           // 1 hour
      complete: `restaurant:complete:${restaurantId}`,         // 15 minutes
    };
    
    await Promise.all([
      this.redis.setex(cacheKeys.operational, 300, JSON.stringify(data.operational)),
      this.redis.setex(cacheKeys.content, 3600, JSON.stringify(data.content)),
      this.redis.setex(cacheKeys.complete, 900, JSON.stringify(data))
    ]);
  }
  
  // Smart cache invalidation
  async invalidateRestaurantCache(restaurantId: string, type: 'operational' | 'content' | 'all' = 'all') {
    const patterns = {
      operational: [`restaurant:operational:${restaurantId}`, `restaurant:complete:${restaurantId}`],
      content: [`restaurant:content:${restaurantId}`, `restaurant:complete:${restaurantId}`],
      all: [`restaurant:*:${restaurantId}`]
    };
    
    const keysToDelete = patterns[type];
    await Promise.all(keysToDelete.map(pattern => this.redis.del(pattern)));
  }
  
  // Preload popular content
  async preloadPopularRestaurants() {
    // Get top 50 restaurants by order volume
    const popularRestaurants = await this.getPopularRestaurants(50);
    
    // Preload their complete data
    await Promise.all(
      popularRestaurants.map(restaurant => 
        hybridResolver.getRestaurantComplete(restaurant.id)
      )
    );
  }
}
```

### **6.2 Image Optimization Pipeline**
```typescript
// src/lib/cloudinary/optimization.ts
// Enhanced image optimization building on your existing Cloudinary setup
export class TapGoImageOptimizer {
  // Automatic image optimization for CMS content
  static optimizeForCMS(publicId: string, context: 'blog' | 'restaurant' | 'menu' | 'promotion') {
    const optimizations = {
      blog: {
        hero: { width: 1200, height: 630, crop: 'fill' },
        thumbnail: { width: 400, height: 250, crop: 'fill' },
        inline: { width: 800, height: 450, crop: 'fit' }
      },
      restaurant: {
        hero: { width: 1920, height: 1080, crop: 'fill' },
        card: { width: 400, height: 300, crop: 'fill' },
        gallery: { width: 800, height: 600, crop: 'fill' }
      },
      menu: {
        main: { width: 600, height: 400, crop: 'fill' },
        thumbnail: { width: 150, height: 150, crop: 'fill' },
        detail: { width: 800, height: 600, crop: 'fit' }
      },
      promotion: {
        banner: { width: 1200, height: 400, crop: 'fill' },
        card: { width: 300, height: 200, crop: 'fill' }
      }
    };
    
    const contextOptimizations = optimizations[context];
    const result = {};
    
    Object.entries(contextOptimizations).forEach(([size, options]) => {
      result[size] = getOptimizedImageUrl(publicId, {
        ...options,
        quality: 'auto',
        format: 'auto',
        dpr: 'auto'
      });
    });
    
    return result;
  }
  
  // Generate responsive image sets
  static generateResponsiveSet(publicId: string, baseWidth: number) {
    const breakpoints = [320, 640, 768, 1024, 1280, 1920];
    
    return {
      srcSet: breakpoints.map(width => {
        const scaledHeight = Math.round((baseWidth * 0.6) * (width / baseWidth));
        const url = getOptimizedImageUrl(publicId, {
          width,
          height: scaledHeight,
          crop: 'fill',
          quality: 'auto',
          format: 'auto'
        });
        return `${url} ${width}w`;
      }).join(', '),
      
      sizes: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
      
      src: getOptimizedImageUrl(publicId, {
        width: baseWidth,
        height: Math.round(baseWidth * 0.6),
        crop: 'fill',
        quality: 'auto',
        format: 'auto'
      })
    };
  }
}
```
