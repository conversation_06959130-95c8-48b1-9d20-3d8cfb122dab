#!/usr/bin/env node

/**
 * Strapi Dependencies Installation Script
 * Installs required packages for Strapi + Neon integration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Installing Strapi + Neon Integration Dependencies...\n');

// Dependencies to install in the main Tap2Go project
const mainProjectDependencies = [
  'axios',           // HTTP client for Strapi API
  'ioredis',         // Redis client for caching
  '@neondatabase/serverless', // Neon database client
];

// Dev dependencies for the main project
const mainProjectDevDependencies = [
  '@types/ioredis',  // TypeScript types for Redis
];

// Function to run shell commands
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error.message);
    process.exit(1);
  }
}

// Function to check if package.json exists
function checkPackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found. Please run this script from the project root.');
    process.exit(1);
  }
  return packageJsonPath;
}

// Function to update package.json with new scripts
function updatePackageJsonScripts() {
  const packageJsonPath = checkPackageJson();
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add new scripts for Strapi integration
  const newScripts = {
    'strapi:setup': 'node scripts/setup-strapi.js',
    'strapi:dev': 'cd tap2go-cms && npm run develop',
    'strapi:build': 'cd tap2go-cms && npm run build',
    'strapi:start': 'cd tap2go-cms && npm run start',
    'cms:cache-clear': 'node scripts/clear-cms-cache.js',
    'cms:sync': 'node scripts/sync-firebase-strapi.js'
  };
  
  packageJson.scripts = {
    ...packageJson.scripts,
    ...newScripts
  };
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json with CMS scripts\n');
}

// Main installation process
async function installDependencies() {
  try {
    // Check if we're in the right directory
    checkPackageJson();
    
    // Install main project dependencies
    if (mainProjectDependencies.length > 0) {
      const depsCommand = `npm install ${mainProjectDependencies.join(' ')}`;
      runCommand(depsCommand, 'Installing main project dependencies');
    }
    
    // Install dev dependencies
    if (mainProjectDevDependencies.length > 0) {
      const devDepsCommand = `npm install -D ${mainProjectDevDependencies.join(' ')}`;
      runCommand(devDepsCommand, 'Installing development dependencies');
    }
    
    // Update package.json with new scripts
    updatePackageJsonScripts();
    
    console.log('🎉 All dependencies installed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Set up Neon database: https://neon.tech');
    console.log('2. Create Strapi project: npm run strapi:setup');
    console.log('3. Configure environment variables in .env.local');
    console.log('4. Start development: npm run dev (main) + npm run strapi:dev (CMS)');
    console.log('\n📖 See docs/ENHANCED_STRAPI_INTEGRATION_PLAN.md for detailed setup instructions.');
    
  } catch (error) {
    console.error('❌ Installation failed:', error.message);
    process.exit(1);
  }
}

// Run the installation
installDependencies();
